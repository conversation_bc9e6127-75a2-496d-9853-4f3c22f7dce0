import { compressForStorage } from '@/utils/imageCompression';

// Storage service interface
export interface StorageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface StorageDeleteResult {
  success: boolean;
  error?: string;
}

// Simplified storage service using only Supabase
export class StorageService {
  /**
   * Upload image with automatic compression
   * This is the recommended method for uploading images from URIs
   */
  static async uploadImageFromUri(
    imageUri: string,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      console.log('Processing image for upload:', { imageUri, path });

      // Compress image for optimal storage
      const compressionResult = await compressForStorage(imageUri);
      console.log('Image compression result:', compressionResult);

      // Convert compressed image to blob
      const response = await fetch(compressionResult.uri);
      if (!response.ok) {
        throw new Error(`Failed to fetch compressed image: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      console.log('Compressed image blob:', { size: blob.size, type: blob.type });

      // Upload the compressed blob
      return this.uploadImage(blob, path, contentType || blob.type);
    } catch (error) {
      console.error('Image processing failed, trying direct upload:', error);

      // Fallback to direct upload without compression
      try {
        const response = await fetch(imageUri);
        if (!response.ok) {
          throw new Error(`Failed to fetch original image: ${response.status} ${response.statusText}`);
        }
        const blob = await response.blob();
        return this.uploadImage(blob, path, contentType || blob.type);
      } catch (fallbackError) {
        console.error('Direct upload also failed:', fallbackError);
        return {
          success: false,
          error: `Image upload failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`,
        };
      }
    }
  }

  static async uploadImage(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    console.log('Uploading to Supabase storage...');
    return await this.uploadToSupabase(file, path, contentType);
  }

  // Test Supabase storage connectivity
  private static async testSupabaseStorage(): Promise<boolean> {
    try {
      const { supabase } = await import('@/lib/supabase');

      // Try to list buckets to test connectivity
      const { data, error } = await supabase.storage.listBuckets();

      if (error) {
        console.error('Supabase storage test failed:', error);
        return false;
      }

      console.log('Supabase storage test successful, available buckets:', data?.map(b => b.name));
      return true;
    } catch (error) {
      console.error('Supabase storage test error:', error);
      return false;
    }
  }

  private static async uploadToSupabase(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      const { supabase } = await import('@/lib/supabase');

      console.log('Attempting Supabase upload:', { path, size: file.size, type: file.type });

      // Test connectivity first
      const isConnected = await this.testSupabaseStorage();
      if (!isConnected) {
        throw new Error('Supabase storage is not accessible');
      }

      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(path, file, {
          contentType: contentType || file.type,
          upsert: true
        });

      if (error) {
        console.error('Supabase storage error details:', error);
        throw error;
      }

      console.log('Supabase upload successful:', data);

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(path);

      console.log('Generated public URL:', publicUrl);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error('Supabase upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  static async deleteImage(path: string): Promise<StorageDeleteResult> {
    try {
      const { supabase } = await import('@/lib/supabase');
      
      const { error } = await supabase.storage
        .from('user-content')
        .remove([path]);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Storage delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }

  // Helper method to generate unique file paths
  static generateImagePath(userId: string, type: 'avatar' | 'scan' | 'diagnosis', extension: string = 'jpg'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${type}s/${userId}-${timestamp}-${random}.${extension}`;
  }

  // Health check method
  static async checkStorageHealth(): Promise<{
    supabaseAvailable: boolean;
    recommendedMethod: 'supabase';
  }> {
    const supabaseAvailable = await this.testSupabaseStorage();

    console.log('Storage health check:', {
      supabaseAvailable,
      recommendedMethod: 'supabase',
    });

    return {
      supabaseAvailable,
      recommendedMethod: 'supabase',
    };
  }
}
