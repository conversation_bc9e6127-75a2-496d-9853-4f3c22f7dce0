#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

async function debugIssues() {
  console.log('🔍 Debugging PlantConnects Issues...\n');

  // Initialize Supabase client
  const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://zlivouxymzpbyoxnwxrp.supabase.co';
  const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_KEY || 'sb_publishable_ABgFmonyDTQFCL9IiD9Pzw_H7-dr7yC';

  const supabase = createClient(supabaseUrl, supabaseKey);

  // 1. Test Supabase Connection
  console.log('1. Testing Supabase Connection...');
  try {
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('❌ Session Error:', sessionError.message);
    } else {
      console.log('✅ Session Status:', session?.session ? 'Authenticated' : 'Not authenticated');
      if (session?.session) {
        console.log('   User ID:', session.session.user.id);
        console.log('   Email:', session.session.user.email);
      }
    }
  } catch (error) {
    console.error('❌ Connection Error:', error);
  }

  // 2. Test Database Connectivity
  console.log('\n2. Testing Database Connectivity...');
  try {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(0);
    
    if (error) {
      console.error('❌ Database Error:', error.message);
    } else {
      console.log('✅ Database connection successful');
    }
  } catch (error) {
    console.error('❌ Database Exception:', error);
  }

  // 3. Test Garden Collections Query
  console.log('\n3. Testing Garden Collections Query...');
  try {
    // Try to get session first
    const { data: session } = await supabase.auth.getSession();
    
    if (session?.session?.user) {
      const { data, error } = await supabase
        .from('garden_collections')
        .select(`
          *,
          plant_identifications (*),
          plant_diagnoses (*)
        `)
        .eq('user_id', session.session.user.id)
        .limit(5);
      
      if (error) {
        console.error('❌ Garden Query Error:', error.message);
        console.error('   Details:', error);
      } else {
        console.log('✅ Garden query successful');
        console.log('   Records found:', data?.length || 0);
        if (data && data.length > 0) {
          console.log('   Sample record:', JSON.stringify(data[0], null, 2));
        }
      }
    } else {
      console.log('⚠️  No authenticated user - skipping garden query');
    }
  } catch (error) {
    console.error('❌ Garden Query Exception:', error);
  }

  // 4. Test OpenRouter Configuration
  console.log('\n4. Testing OpenRouter Configuration...');
  const openrouterApiKey = process.env.OPENROUTER_API_KEY;
  const openrouterModel = process.env.OPENROUTER_MODEL;
  
  if (!openrouterApiKey) {
    console.error('❌ OPENROUTER_API_KEY not found in environment');
  } else {
    console.log('✅ OPENROUTER_API_KEY found');
    console.log('   Key prefix:', openrouterApiKey.substring(0, 10) + '...');
  }
  
  if (!openrouterModel) {
    console.error('❌ OPENROUTER_MODEL not found in environment');
  } else {
    console.log('✅ OPENROUTER_MODEL:', openrouterModel);
  }

  // 5. Test OpenRouter API (simple ping)
  console.log('\n5. Testing OpenRouter API...');
  try {
    const response = await fetch('https://openrouter.ai/api/v1/models', {
      headers: {
        'Authorization': `Bearer ${openrouterApiKey}`,
        'HTTP-Referer': 'https://plantidguide.com',
        'X-Title': 'PlantIDGuide: Plant Identifier',
      },
    });
    
    if (response.ok) {
      console.log('✅ OpenRouter API accessible');
    } else {
      console.error('❌ OpenRouter API Error:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('❌ OpenRouter API Exception:', error);
  }

  console.log('\n🏁 Debug complete!');
}

// Run the debug script
debugIssues().catch(console.error);
